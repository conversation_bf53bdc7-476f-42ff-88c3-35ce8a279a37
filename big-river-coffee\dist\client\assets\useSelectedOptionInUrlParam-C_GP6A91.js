import{r as o}from"./chunk-D4RADZKF-BzETJbTh.js";import{m as n}from"./getProductOptions-DRO1RMF1.js";function i(r){return o.useEffect(()=>{const e=new URLSearchParams(n(r||[])),t=new URLSearchParams(window.location.search),a=new URLSearchParams({...Object.fromEntries(t),...Object.fromEntries(e)});a.size>0&&window.history.replaceState({},"",`${window.location.pathname}?${a.toString()}`)},[JSON.stringify(r)]),null}export{i as u};
