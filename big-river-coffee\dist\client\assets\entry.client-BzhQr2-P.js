import{j as R}from"./jsx-runtime-C_22QpLb.js";import{r as t,k as g,l as v,m as p,n as y,o as h,p as M,q as _,s as D,t as S,v as b,w as C,x as P,y as E,z as F,B as z,C as H}from"./chunk-D4RADZKF-BzETJbTh.js";import{r as L,a as I}from"./index-BIBkYi5k.js";/**
 * react-router v7.6.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function O(o){return t.createElement(h,{flushSync:L.flushSync,...o})}var e=null,c=null;function k(){if(!e&&window.__reactRouterContext&&window.__reactRouterManifest&&window.__reactRouterRouteModules){if(window.__reactRouterManifest.sri===!0){const o=document.querySelector("script[rr-importmap]");if(o!=null&&o.textContent)try{window.__reactRouterManifest.sri=JSON.parse(o.textContent).integrity}catch(s){console.error("Failed to parse import map",s)}}e={context:window.__reactRouterContext,manifest:window.__reactRouterManifest,routeModules:window.__reactRouterRouteModules,stateDecodingPromise:void 0,router:void 0,routerInitialized:!1}}}function q({unstable_getContext:o}){var u,m;if(k(),!e)throw new Error("You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`");let s=e;if(!e.stateDecodingPromise){let a=e.context.stream;g(a,"No stream found for single fetch decoding"),e.context.stream=void 0,e.stateDecodingPromise=M(a,window).then(n=>{e.context.state=n.value,s.stateDecodingPromise.value=!0}).catch(n=>{s.stateDecodingPromise.error=n})}if(e.stateDecodingPromise.error)throw e.stateDecodingPromise.error;if(!e.stateDecodingPromise.value)throw e.stateDecodingPromise;let l=_(e.manifest.routes,e.routeModules,e.context.state,e.context.ssr,e.context.isSpaMode),r;if(e.context.isSpaMode){let{loaderData:a}=e.context.state;(u=e.manifest.routes.root)!=null&&u.hasLoader&&a&&"root"in a&&(r={loaderData:{root:a.root}})}else r=D(e.context.state,l,a=>{var n,f,x;return{clientLoader:(n=e.routeModules[a])==null?void 0:n.clientLoader,hasLoader:((f=e.manifest.routes[a])==null?void 0:f.hasLoader)===!0,hasHydrateFallback:((x=e.routeModules[a])==null?void 0:x.HydrateFallback)!=null}},window.location,(m=window.__reactRouterContext)==null?void 0:m.basename,e.context.isSpaMode),r&&r.errors&&(r.errors=S(r.errors));let i=b({routes:l,history:E(),basename:e.context.basename,unstable_getContext:o,hydrationData:r,hydrationRouteProperties:H,mapRouteProperties:z,future:{unstable_middleware:e.context.future.unstable_middleware},dataStrategy:P(()=>i,e.manifest,e.routeModules,e.context.ssr,e.context.basename),patchRoutesOnNavigation:C(e.manifest,e.routeModules,e.context.ssr,e.context.routeDiscovery,e.context.isSpaMode,e.context.basename)});return e.router=i,i.state.initialized&&(e.routerInitialized=!0,i.initialize()),i.createRoutesForHMR=F,window.__reactRouterDataRouter=i,i}function j(o){c||(c=q({unstable_getContext:o.unstable_getContext}));let[s,l]=t.useState(void 0),[r,i]=t.useState(c.state.location);return t.useLayoutEffect(()=>{e&&e.router&&!e.routerInitialized&&(e.routerInitialized=!0,e.router.initialize())},[]),t.useLayoutEffect(()=>{if(e&&e.router)return e.router.subscribe(u=>{u.location!==r&&i(u.location)})},[r]),g(e,"ssrInfo unavailable for HydratedRouter"),v(c,e.manifest,e.routeModules,e.context.ssr,e.context.routeDiscovery,e.context.isSpaMode),t.createElement(t.Fragment,null,t.createElement(p.Provider,{value:{manifest:e.manifest,routeModules:e.routeModules,future:e.context.future,criticalCss:s,ssr:e.context.ssr,isSpaMode:e.context.isSpaMode,routeDiscovery:e.context.routeDiscovery}},t.createElement(y,{location:r},t.createElement(O,{router:c}))),t.createElement(t.Fragment,null))}var d={},w;function N(){if(w)return d;w=1;var o=I();return d.createRoot=o.createRoot,d.hydrateRoot=o.hydrateRoot,d}var B=N();window.location.origin.includes("webcache.googleusercontent.com")||t.startTransition(()=>{B.hydrateRoot(document,R.jsx(t.StrictMode,{children:R.jsx(j,{})}))});
