import{w as ut}from"./with-props-CbdrmLzL.js";import{j as e}from"./jsx-runtime-C_22QpLb.js";import{u as mt,r as u,c as ht,a as xt,d as ft}from"./chunk-D4RADZKF-BzETJbTh.js";import{P as gt}from"./ProductCard-Bh04Afjv.js";import{A as Qe}from"./AddToCartButton-PbdfRk7P.js";import{u as pt}from"./Aside-DOuHpYsm.js";import{O as bt}from"./OptimizedVideo-Dqf1l_zF.js";import{i as vt}from"./scrollAnimations-D2F8dg76.js";import{M as yt}from"./Money-CflnwKbq.js";import{g as jt}from"./getProductOptions-DRO1RMF1.js";import"./variants-0wbUisc9.js";import"./Image-aRbI9IFn.js";import"./index-CtaMWxPM.js";const Gt=()=>[{title:"All Coffees | Big River Coffee"},{description:"Discover our complete collection of premium coffee - ethically sourced and expertly roasted for every adventure"}],Yt=ut(function(){var ze,Oe,_e,Ee,$e,He,Ve;const{products:b,pageInfo:Nt,queryFilter:kt,subscriptionPromoProduct:Ue}=mt(),G=t=>{const r=t.title.toLowerCase(),l=t.tags||[];return r.includes("box")||r.includes("bundle")||l.some(o=>o.toLowerCase().includes("bundle"))?"bundles":r.includes("k-cup")||r.includes("kcup")||l.some(o=>o.toLowerCase().includes("k-cup"))?"kcups":r.includes("mug")||r.includes("cup")||r.includes("tumbler")||r.includes("gear")||r.includes("merchandise")||l.some(o=>o.toLowerCase().includes("gear"))?"gear":"coffee"},ge=(((ze=b==null?void 0:b.nodes)==null?void 0:ze.filter(t=>G(t)==="kcups"))||[])[0],[Je,Ze]=u.useState(((_e=(Oe=ge==null?void 0:ge.variants)==null?void 0:Oe.nodes)==null?void 0:_e[0])||null),[Xe,Ce]=u.useState(1),[S]=ht(),et=xt(),[St,Lt]=u.useState((b==null?void 0:b.nodes)||[]),[ee,Se]=u.useState("grid"),[Le,tt]=u.useState("featured"),[Pt,Ft]=u.useState(!1),[Mt,Bt]=u.useState(!0),[w,te]=u.useState("coffee"),[se,re]=u.useState(!1),[Rt,Tt]=u.useState(!1),ae=u.useRef(null),pe=u.useRef(null),ne=u.useRef(null),oe=u.useRef(null),le=u.useRef(null),Pe=S.get("roast")||"all",Fe=S.get("kcup-type")||"all",Me=S.get("gear-category")||"all",Be=S.get("price")||"all",Re=S.get("section")||"coffee",E={coffee:((Ee=b==null?void 0:b.nodes)==null?void 0:Ee.filter(t=>G(t)==="coffee"))||[],bundles:(($e=b==null?void 0:b.nodes)==null?void 0:$e.filter(t=>G(t)==="bundles"))||[],kcups:((He=b==null?void 0:b.nodes)==null?void 0:He.filter(t=>G(t)==="kcups"))||[],gear:((Ve=b==null?void 0:b.nodes)==null?void 0:Ve.filter(t=>G(t)==="gear"))||[]},st=(t,r)=>{let l=[...r];switch(t==="coffee"&&Pe!=="all"?l=l.filter(o=>o.title.toLowerCase().includes(Pe)):t==="kcups"&&Fe!=="all"?l=l.filter(o=>{const a=o.title.toLowerCase();switch(Fe){case"single-origin":return a.includes("single")||a.includes("origin");case"blend":return a.includes("blend");case"flavored":return a.includes("vanilla")||a.includes("caramel")||a.includes("hazelnut")||a.includes("flavored");default:return!0}}):t==="gear"&&Me!=="all"&&(l=l.filter(o=>{const a=o.title.toLowerCase();switch(Me){case"mugs":return a.includes("mug")||a.includes("cup")||a.includes("tumbler");case"equipment":return a.includes("grinder")||a.includes("equipment")||a.includes("machine");case"apparel":return a.includes("shirt")||a.includes("hat")||a.includes("apparel");case"accessories":return a.includes("accessory")||a.includes("gear")||!a.includes("mug")&&!a.includes("cup")&&!a.includes("grinder")&&!a.includes("shirt");default:return!0}})),Be!=="all"&&(l=l.filter(o=>{const a=parseFloat(o.priceRange.minVariantPrice.amount);switch(Be){case"under-25":return a<25;case"25-50":return a>=25&&a<=50;case"over-50":return a>50;default:return!0}})),Le){case"price-low":l.sort((o,a)=>parseFloat(o.priceRange.minVariantPrice.amount)-parseFloat(a.priceRange.minVariantPrice.amount));break;case"price-high":l.sort((o,a)=>parseFloat(a.priceRange.minVariantPrice.amount)-parseFloat(o.priceRange.minVariantPrice.amount));break;case"name":l.sort((o,a)=>o.title.localeCompare(a.title));break}return l};u.useEffect(()=>{se||te(Re)},[Re,se]),u.useEffect(()=>{const t=S.get("section");t&&["coffee","bundles","kcups","subscriptions","gear"].includes(t)&&(te(t),setTimeout(()=>{const r=t==="coffee"?ae:t==="bundles"?pe:t==="kcups"?ne:t==="subscriptions"?oe:le;if(r.current){const K=r.current.offsetTop,$=Math.max(0,K-188);window.scrollTo({top:$,behavior:"smooth"})}},300))},[S]),u.useEffect(()=>{let t,r=!1;const l=()=>{se||r||(r=!0,clearTimeout(t),t=setTimeout(()=>{try{const o=[{name:"coffee",ref:ae},{name:"kcups",ref:ne},{name:"subscriptions",ref:oe},{name:"gear",ref:le}],a=window.pageYOffset,W=120-32+80+20;let F=w;for(const M of o)if(M.ref.current){const Q=M.ref.current.offsetTop-W,c=Q+M.ref.current.offsetHeight;if(a>=Q&&a<c){F=M.name;break}}if(F!==w){te(F);const M=new URLSearchParams(S);M.set("section",F),window.history.replaceState({},"",`?${M.toString()}`)}}catch(o){console.error("Scroll handling error:",o)}finally{r=!1}},150))};return window.addEventListener("scroll",l,{passive:!0}),()=>{window.removeEventListener("scroll",l),clearTimeout(t)}},[w,S,se]),u.useEffect(()=>{const t=()=>{const a=document.querySelector(".collections-mobile-nav");a&&(a.style.display=window.innerWidth<1024?"block":"none",a.style.visibility="visible");const T=document.querySelector(".collections-desktop-nav");T&&(T.style.display=window.innerWidth>=1024?"flex":"none",T.style.visibility="visible")},r=setTimeout(t,10),l=setTimeout(t,100),o=setTimeout(t,500);return window.addEventListener("resize",t),document.addEventListener("visibilitychange",t),()=>{clearTimeout(r),clearTimeout(l),clearTimeout(o),window.removeEventListener("resize",t),document.removeEventListener("visibilitychange",t)}},[]);const rt=t=>{tt(t)},Te=t=>{try{re(!0),te(t);const l={coffee:ae,bundles:pe,kcups:ne,subscriptions:oe,gear:le}[t];if(l.current){const $=l.current.offsetTop,W=Math.max(0,$-188),F=new URLSearchParams(S);F.set("section",t),window.history.replaceState({},"",`?${F.toString()}`),window.scrollTo({top:W,behavior:"smooth"}),setTimeout(()=>{re(!1)},1200)}else console.warn(`Section ref not found for: ${t}`),re(!1)}catch(r){console.error("Error in scrollToSection:",r),re(!1)}},be=t=>{const r={coffee:E.coffee.length,bundles:E.bundles.length,kcups:E.kcups.length,subscriptions:1,gear:E.gear.length};return{coffee:{title:"Coffee",description:"Premium coffee beans, ethically sourced and expertly roasted",icon:e.jsxs("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{d:"M2 21h18v-2H2v2zM20 8h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v8c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.1 0 2-.9 2-2s-.9-2-2-2zM16 13c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V5h12v8z"}),e.jsx("path",{d:"M20 10c.55 0 1-.45 1-1s-.45-1-1-1v2z"})]}),count:r.coffee},bundles:{title:"Bundles",description:"Curated coffee bundles and gift boxes",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1v6m6-6v6"})}),count:r.bundles},kcups:{title:"K-Cups",description:"Convenient single-serve coffee pods for your Keurig",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),count:r.kcups},gear:{title:"Gear",description:"Coffee equipment, mugs, and adventure gear",icon:e.jsxs("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),count:r.gear},subscriptions:{title:"Subscriptions",description:"Never run out of coffee with our flexible subscription service",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),count:r.subscriptions}}[t]};return u.useEffect(()=>{if(!(typeof window>"u"))return document.body.classList.add("collections-page"),()=>{document.body.classList.remove("collections-page")}},[]),u.useEffect(()=>vt(),[]),e.jsxs(e.Fragment,{children:[e.jsx("style",{children:`
        body.collections-page {
          margin: 0 !important;
          padding: 0 !important;
        }
        body.collections-page main {
          padding: 0 !important;
          margin: 0 !important;
        }
      `}),e.jsx("div",{className:"hidden sm:block sticky top-0 w-full h-screen z-0",children:e.jsx("div",{className:"w-full h-full",children:e.jsx(bt,{src:"/newhomepage/shop_hero_vid.mp4",poster:"/newhomepage/shop_stillframe.webp",fallbackImage:"/newhomepage/shop_stillframe.png",className:"w-full h-full object-cover",style:{width:"100%",height:"100%",display:"block"},autoPlay:!0,muted:!0,loop:!0,playsInline:!0,preload:"metadata",lazy:!1,forceMobileImage:!0})})}),e.jsxs("div",{className:"relative z-10 min-h-screen bg-gray-50",style:{marginTop:"0",paddingTop:"0"},children:[e.jsx("div",{className:"sticky z-40 bg-white border-b border-neutral-200 shadow-sm top-0",children:e.jsx("div",{className:"container-clean",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between py-4 gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"hidden lg:flex space-x-2",children:["coffee","bundles","kcups","subscriptions","gear"].map(t=>{const r=be(t),l=w===t;return e.jsxs("button",{type:"button",onClick:o=>{o.preventDefault(),o.stopPropagation(),Te(t)},className:`relative flex items-center px-4 py-2 rounded-lg font-semibold transition-all duration-300 ease-out whitespace-nowrap transform hover:scale-105 hover:-translate-y-0.5 ${l?"bg-army-600 text-white shadow-lg hover:bg-army-700":"text-neutral-600 hover:text-army-600 hover:bg-army-50 hover:shadow-md"}`,children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:r.icon}),e.jsx("span",{className:"mr-3",children:r.title}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 ${l?"bg-white/20 text-white":"bg-army-100 text-army-800"}`,children:r.count})]},`desktop-${t}`)})}),e.jsx("div",{className:"lg:hidden",children:e.jsx("div",{className:"grid grid-cols-2 gap-3",children:["coffee","bundles","kcups","subscriptions","gear"].map(t=>{const r=be(t),l=w===t;return e.jsxs("button",{type:"button",onClick:o=>{o.preventDefault(),o.stopPropagation(),Te(t)},className:`flex items-center justify-center px-3 py-3 rounded-lg font-medium transition-all duration-300 ease-out transform hover:scale-105 ${l?"bg-army-600 text-white shadow-lg hover:bg-army-700":"bg-white text-neutral-700 border border-neutral-200 hover:border-army-300 hover:text-army-600 hover:shadow-md"}`,children:[e.jsx("span",{className:"mr-2 flex-shrink-0",children:r.icon}),e.jsx("span",{className:"text-sm font-semibold",children:r.title})]},`mobile-${t}`)})})})]}),e.jsxs("div",{className:"hidden lg:flex items-center space-x-4",children:[e.jsxs("select",{value:Le,onChange:t=>rt(t.target.value),className:"border border-neutral-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-army-500 min-w-[140px] transition-all duration-300 ease-out hover:border-army-400 hover:shadow-md cursor-pointer",children:[e.jsx("option",{value:"featured",children:"Featured"}),e.jsx("option",{value:"name",children:"Name A-Z"}),e.jsx("option",{value:"price-low",children:"Price: Low to High"}),e.jsx("option",{value:"price-high",children:"Price: High to Low"})]}),e.jsxs("div",{className:"flex items-center border border-neutral-300 rounded-lg overflow-hidden bg-white shadow-sm",children:[e.jsx("button",{onClick:()=>Se("grid"),className:`relative flex items-center justify-center w-12 h-12 transition-all duration-300 ease-out transform hover:scale-105 ${ee==="grid"?"bg-army-600 text-white shadow-md hover:bg-army-700":"bg-white text-neutral-600 hover:bg-army-50 hover:text-army-600 hover:shadow-md"}`,title:"Grid View","aria-label":"Grid View",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})})}),e.jsx("div",{className:"w-px h-6 bg-neutral-300"}),e.jsx("button",{onClick:()=>Se("list"),className:`relative flex items-center justify-center w-12 h-12 transition-all duration-300 ease-out transform hover:scale-105 ${ee==="list"?"bg-army-600 text-white shadow-md hover:bg-army-700":"bg-white text-neutral-600 hover:bg-army-50 hover:text-army-600 hover:shadow-md"}`,title:"List View","aria-label":"List View",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})})})]})]})]})})}),e.jsxs("div",{className:"container-clean py-12",children:[Ue&&e.jsx("div",{className:"mb-8",children:e.jsx("div",{className:"bg-gradient-to-r from-army-600 to-army-700 rounded-2xl shadow-xl overflow-hidden border-2 border-army-500",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center p-8 gap-8",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-32 h-32 md:w-40 md:h-40 bg-white rounded-full overflow-hidden shadow-lg border-4 border-white",children:e.jsx("img",{src:"/br_bundlepic.png",alt:"Big River Coffee Bundle",className:"w-full h-full object-cover",loading:"eager"})})}),e.jsx("div",{className:"flex-1 text-center md:text-left",children:e.jsxs("div",{className:"text-white",children:[e.jsxs("div",{className:"flex flex-wrap justify-center md:justify-start items-center gap-3 mb-4",children:[e.jsx("span",{className:"bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg",children:"🔥 LIMITED TIME"}),e.jsx("span",{className:"bg-yellow-400 text-army-800 px-4 py-2 rounded-full text-sm font-bold shadow-lg",children:"☕ FIRST ONE FREE"})]}),e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4 text-white",style:{fontFamily:"var(--font-title)"},children:"Start Your Coffee Journey"}),e.jsx("div",{className:"text-center md:text-left mb-6 border-2 border-army-200 rounded-lg p-4 bg-army-600/10",children:e.jsxs("p",{className:"text-army-100 text-lg md:text-xl leading-relaxed font-serif",children:["Get your ",e.jsx("strong",{children:"first coffee bag absolutely FREE"})," when you start a subscription! Save 15% on every order + free shipping over $30."]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-center justify-center md:justify-start",children:[e.jsxs("a",{href:"/products/first-one-free",className:"bg-orange-500 hover:bg-orange-600 px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl inline-flex items-center gap-3 shadow-lg border-4 border-orange-700 font-serif",children:[e.jsx("span",{className:"text-white",children:"View Deal"}),e.jsx("svg",{className:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M13.025 1l-2.847 2.828 6.176 6.176h-16.354v3.992h16.354l-6.176 6.176 2.847 2.828 10.975-11z"})})]}),e.jsxs("div",{className:"flex items-center text-army-100",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm font-medium",children:"No commitment • Cancel anytime"})]})]})]})})]})})}),e.jsx("div",{className:"flex flex-col lg:flex-row gap-8",children:e.jsx("div",{className:"flex-1",children:["coffee","bundles","kcups","subscriptions","gear"].map(t=>{var a,T,Y,K,$,W,F,M,Q;const r=be(t),l=t==="subscriptions"?[]:E[t],o=t==="subscriptions"?[]:st(t,l);if(t==="kcups"){const c=o[0];if(c&&o.length>0){const L=((a=c.variants)==null?void 0:a.nodes)||[],y=L.filter(x=>x.availableForSale),A=y[0]||L[0];if(!A)return null;const m=Je||A,B=Xe,g=x=>{var k;if(x.title&&x.title!=="Default Title")return x.title;const i=(k=x.selectedOptions)==null?void 0:k.find(j=>j.name.toLowerCase().includes("flavor")||j.name.toLowerCase().includes("title")||j.name.toLowerCase().includes("variant")||j.name.toLowerCase().includes("type"));return i!=null&&i.value?i.value:x.title||`K-Cup Flavor ${x.id.split("/").pop()}`},O=x=>{var k;const i=g(x).toLowerCase();return i.includes("dark")||i.includes("colombian")?"/dark_colombian_kcup.webp":i.includes("vanilla")||i.includes("french")?"/french_vanilla_kcup.webp":i.includes("maple")||i.includes("bacon")?"/maple_bacon_kcup.webp":i.includes("medium")||i.includes("nicaraguan")?"/medium_nicaraguan_kcup.webp":((k=c.featuredImage)==null?void 0:k.url)||"/medium_nicaraguan_kcup.webp"},H=x=>{var k;const i=g(x).toLowerCase();return i.includes("dark")||i.includes("colombian")?"/dark_colombian_kcup.png":i.includes("vanilla")||i.includes("french")?"/french_vanilla_kcup.png":i.includes("maple")||i.includes("bacon")?"/maple_bacon_kcup.png":i.includes("medium")||i.includes("nicaraguan")?"/medium_nicaraguan_kcup.png":((k=c.featuredImage)==null?void 0:k.url)||"/medium_nicaraguan_kcup.png"},U=m.id===A.id&&!m._userSelected,N=x=>{Ze({...x,_userSelected:!0})};return e.jsxs("div",{ref:ne,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"188px"},children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600",children:r.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-3xl font-bold transition-colors duration-300 ease-out ${w===t?"text-army-700":"text-gray-900"}`,style:{fontFamily:"var(--font-title)"},children:r.title}),e.jsx("p",{className:"text-gray-600",children:r.description})]})]})}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ease-out ${w===t?"bg-army-600 w-24":"bg-gray-200 w-16"}`})]}),e.jsx("div",{className:"bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-4 sm:p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[e.jsx("div",{className:"kcup-image-container aspect-square bg-white rounded-xl overflow-hidden shadow-sm max-w-sm mx-auto lg:max-w-md lg:mx-0",children:e.jsxs("picture",{children:[e.jsx("source",{srcSet:U?((T=c.featuredImage)==null?void 0:T.url)||"/medium_nicaraguan_kcup.webp":O(m),type:"image/webp"}),e.jsx("img",{src:U?((Y=c.featuredImage)==null?void 0:Y.url)||"/medium_nicaraguan_kcup.png":H(m),alt:`${g(m)} K-Cup`,className:"w-full h-full object-contain p-4 transition-all duration-300 ease-in-out"})]})}),e.jsxs("div",{className:"flex flex-col justify-between space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",style:{fontFamily:"var(--font-title)"},children:"Big River K-Cups"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600 mb-4",children:"20 count single-serve pods • Compatible with Keurig 1.0 & 2.0"}),e.jsx("div",{className:"text-2xl sm:text-3xl font-bold text-army-700 mb-4 sm:mb-6",children:e.jsx(yt,{data:c.priceRange.minVariantPrice})}),e.jsxs("div",{className:"mb-4 sm:mb-6",children:[e.jsx("label",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-3 block",children:"Flavors"}),y.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3",children:y.map(x=>{const i=g(x);return e.jsx("button",{onClick:()=>N(x),className:`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 ease-out text-left min-h-[48px] transform hover:scale-105 hover:-translate-y-0.5 ${(m==null?void 0:m.id)===x.id?"border-army-600 bg-army-600 text-white shadow-md hover:bg-army-700 hover:shadow-lg":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600 hover:bg-army-50 hover:shadow-md"}`,children:i},x.id)})}):L.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3",children:L.map(x=>{const i=g(x);return e.jsxs("button",{onClick:()=>N(x),disabled:!x.availableForSale,className:`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 text-left min-h-[48px] ${(m==null?void 0:m.id)===x.id?"border-amber-600 bg-amber-600 text-white shadow-md":x.availableForSale?"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600":"border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"}`,children:[i," ",!x.availableForSale&&"(Out of Stock)"]},x.id)})}):e.jsx("div",{className:"text-gray-500 text-sm",children:"No flavor options available"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between sm:justify-start sm:space-x-4",children:[e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>Ce(Math.max(1,B-1)),className:"w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 hover:bg-army-50 transition-all duration-300 ease-out transform hover:scale-105 min-h-[44px] min-w-[44px]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"text-lg sm:text-xl font-semibold text-gray-900 min-w-[3rem] text-center",children:B}),e.jsx("button",{onClick:()=>Ce(B+1),className:"w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 hover:bg-army-50 transition-all duration-300 ease-out transform hover:scale-105 min-h-[44px] min-w-[44px]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]})]}),e.jsx(Qe,{disabled:!m||!m.availableForSale||!m.id,lines:m!=null&&m.id?[{merchandiseId:m.id,quantity:B}]:[],className:"w-full bg-army-600 hover:bg-army-700 hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:hover:scale-100 text-white py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold text-base sm:text-lg transition-all duration-300 ease-out min-h-[48px] hover:shadow-lg",children:m?m.availableForSale?"Add to cart":"Out of stock":"Select a flavor"})]})]})]})}),e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Available Flavors"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4",children:[e.jsx("div",{className:"aspect-square bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200",children:e.jsx("img",{src:"/dark_colombian_kcup.png",alt:"Dark Colombian K-Cup",className:"w-full h-full object-contain p-3",loading:"lazy"})}),e.jsx("div",{className:"aspect-square bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200",children:e.jsx("img",{src:"/french_vanilla_kcup.png",alt:"French Vanilla K-Cup",className:"w-full h-full object-contain p-3",loading:"lazy"})}),e.jsx("div",{className:"aspect-square bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200",children:e.jsx("img",{src:"/maple_bacon_kcup.png",alt:"Maple Bacon K-Cup",className:"w-full h-full object-contain p-3",loading:"lazy"})}),e.jsx("div",{className:"aspect-square bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200",children:e.jsx("img",{src:"/medium_nicaraguan_kcup.png",alt:"Medium Nicaraguan K-Cup",className:"w-full h-full object-contain p-3",loading:"lazy"})})]})]})]},t)}}if(t==="subscriptions"){const[c,L]=u.useState(""),[y,A]=u.useState(""),[m,B]=u.useState(""),[g,O]=u.useState(""),[H,U]=u.useState(""),[N,x]=u.useState(""),[i,k]=u.useState(""),[j,ve]=u.useState(""),[ie,Ie]=u.useState(1),[ce,at]=u.useState("monthly"),[ye,je]=u.useState(!1),we=u.useRef(null),h=ft(),{open:nt}=pt();u.useEffect(()=>{const s=p=>{we.current&&!we.current.contains(p.target)&&je(!1)};return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[]);const ot=()=>E.coffee||[],lt=(s,p=[])=>{const v=new FormData;v.append("action","fetchProduct"),v.append("handle",s),v.append("selectedOptions",JSON.stringify(p)),h.submit(v,{method:"POST"})},it=()=>{var s;return((s=E.kcups)==null?void 0:s[0])||null},qe=s=>{var p,v;if(!s)return[];try{return jt({...s,selectedOrFirstAvailableVariant:s.selectedOrFirstAvailableVariant||((v=(p=s.variants)==null?void 0:p.nodes)==null?void 0:v[0])})}catch(P){return console.error("Error getting product options:",P),[]}},ct=(s,p)=>{var v;return(v=s==null?void 0:s.variants)!=null&&v.nodes?s.variants.nodes.find(P=>Object.entries(p).every(([V,C])=>{var _;return(_=P.selectedOptions)==null?void 0:_.some(d=>{const f=d.name.toLowerCase().trim(),n=V.toLowerCase().trim(),me=f===n||f==="type"&&n==="grind"||f==="grind"&&n==="type"||f==="grind type"&&n==="type"||f==="type"&&n==="grind type",he=d.value===C;return me&&he})})):null},Ke=s=>{L(s),A(""),B(""),O(""),U(""),x(""),k(""),ve("")},We=ot(),de=it(),J=We.find(s=>s.id===y);u.useEffect(()=>{y&&J&&(B(""),O(""),lt(J.handle,[]))},[y]);const ue=[{value:"weekly",label:"1 Week",sellingPlanId:"gid://shopify/SellingPlan/9581953339"},{value:"monthly",label:"1 Month",sellingPlanId:"gid://shopify/SellingPlan/9581986107"},{value:"3weeks",label:"3 Weeks",sellingPlanId:"gid://shopify/SellingPlan/9582018875"},{value:"6weeks",label:"6 Weeks",sellingPlanId:"gid://shopify/SellingPlan/9582051643"}],dt=ue.find(s=>s.value===ce)||ue[1];return e.jsxs("div",{ref:oe,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"188px"},children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600",children:r.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-3xl font-bold transition-colors duration-200 ${w===t?"text-amber-600":"text-gray-900"}`,style:{fontFamily:"var(--font-title)"},children:"Big River Coffee Subscriptions"}),e.jsx("p",{className:"text-gray-600",children:"Never run out of coffee with our flexible subscription service"})]})]})}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${w===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),e.jsx("div",{className:"bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsx("div",{className:"aspect-square bg-white rounded-xl overflow-hidden shadow-sm",children:e.jsxs("picture",{children:[e.jsx("source",{srcSet:"/subscriptionimgnew.webp",type:"image/webp"}),e.jsx("img",{src:"/subscriptionimgnew.png",alt:"Big River Coffee Subscription",className:"w-full h-full object-center object-cover"})]})}),e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900",style:{fontFamily:"var(--font-title)"},children:"Big River Coffee Subscription"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("span",{className:"bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold",children:"15% OFF"}),e.jsx("span",{className:"bg-green-600 text-white px-3 py-1 rounded-full text-sm font-bold",children:"FIRST ONE FREE"})]})]}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Fresh coffee delivered to your doorstep • Save 15% on every order • First subscription FREE • Free shipping on orders over $30"}),e.jsxs("div",{className:"mb-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e.jsx("span",{className:"font-semibold text-green-700",children:"Get your first subscription delivery FREE"})]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Save your money with up to 15% off every order"]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Delay, modify & cancel anytime"]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Simplify your month with automatic delivery"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Choose Your Product"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsx("button",{onClick:()=>Ke("coffee"),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${c==="coffee"?"border-army-600 bg-army-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600"}`,children:"Coffee Beans"}),e.jsx("button",{onClick:()=>Ke("kcups"),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${c==="kcups"?"border-amber-600 bg-amber-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600"}`,children:"K-Cups"})]})]}),c==="coffee"&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Select Coffee Product"}),e.jsx("div",{className:"relative",children:e.jsxs("select",{value:y,onChange:s=>{A(s.target.value),B(""),O(""),x(""),k(""),ve("")},className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose a coffee..."}),We.map(s=>e.jsx("option",{value:s.id,children:s.title},s.id))]})})]}),c==="coffee"&&y&&e.jsx("div",{className:"mb-6 transition-all duration-200",children:h.state==="submitting"||h.state==="loading"?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Loading product options..."})]}):(K=h.data)!=null&&K.product?e.jsx("div",{className:"space-y-4",children:(()=>{var _;const s=qe(h.data.product),p=h.data.product.id==="gid://shopify/Product/8965076156731"||h.data.product.title.toLowerCase().includes("build your own"),v=h.data.product.id==="gid://shopify/Product/10111589941563"||h.data.product.title.toLowerCase().includes("blend box"),P=h.data.product.id==="gid://shopify/Product/10111587647803"||h.data.product.title.toLowerCase().includes("roasters box");if(p||s.some(d=>d.name.includes("Flavor #"))){const d=s.find(n=>n.name==="Flavor #1"),f=((_=d==null?void 0:d.optionValues)==null?void 0:_.map(n=>n.name))||[];return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Build Your Own Bundle"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Pick 3 flavors for your custom coffee bundle"})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #1"}),e.jsxs("select",{value:N,onChange:n=>x(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-all duration-300 ease-out text-sm hover:border-army-400 hover:shadow-md cursor-pointer",children:[e.jsx("option",{value:"",children:"Choose..."}),f.map(n=>e.jsx("option",{value:n,children:n},n))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #2"}),e.jsxs("select",{value:i,onChange:n=>k(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),f.map(n=>e.jsx("option",{value:n,children:n},n))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #3"}),e.jsxs("select",{value:j,onChange:n=>ve(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),f.map(n=>e.jsx("option",{value:n,children:n},n))]})]})]}),N&&i&&j&&e.jsx("div",{className:"bg-army-50 border border-army-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm font-medium text-army-800",children:["Your Bundle: ",N," + ",i," + ",j]})})]})}if(v){const d=s.find(n=>n.name==="Box Selection"),f=s.find(n=>n.name==="Type");return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Blend Box Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Choose your blend combination"})]}),d&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Blend Combination"}),e.jsxs("select",{value:m,onChange:n=>B(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose combination..."}),d.optionValues.map(n=>e.jsx("option",{value:n.name,children:n.name},n.name))]})]}),f&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Grind Type"}),e.jsxs("select",{value:g,onChange:n=>O(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose grind type..."}),f.optionValues.map(n=>e.jsx("option",{value:n.name,children:n.name},n.name))]})]})]})}if(P){const d=s.find(f=>f.name==="Type"||f.name==="Grind"||f.name==="Grind Type"||f.name.toLowerCase().includes("type")||f.name.toLowerCase().includes("grind"));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Roasters Box Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select your grind preference"})]}),d?e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:d.name}),e.jsxs("select",{value:g,onChange:f=>O(f.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsxs("option",{value:"",children:["Choose ",d.name.toLowerCase(),"..."]}),d.optionValues.map(f=>e.jsx("option",{value:f.name,children:f.name},f.name))]})]}):e.jsx("div",{className:"text-gray-500 text-sm",children:"No variant options available for this product."})]})}const V=s.find(d=>d.name.toLowerCase()==="size"),C=s.find(d=>{const f=d.name.toLowerCase();return f==="type"||f==="grind"||f==="grind type"});return e.jsxs(e.Fragment,{children:[V&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Size"}),e.jsxs("select",{value:m,onChange:d=>B(d.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose size..."}),V.optionValues.map(d=>e.jsx("option",{value:d.name,children:d.name},d.name))]})]}),C&&C.optionValues&&C.optionValues.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:C.name==="type"?"Type":C.name}),e.jsxs("select",{value:g,onChange:d=>O(d.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsxs("option",{value:"",children:["Choose ",C.name.toLowerCase(),"..."]}),C.optionValues.map(d=>e.jsx("option",{value:d.name,children:d.name},d.name))]})]})]})})()}):($=h.data)!=null&&$.error?e.jsxs("div",{className:"text-center py-4 text-red-500",children:["Error loading product options: ",h.data.error]}):e.jsx("div",{className:"text-center py-4 text-gray-500",children:"Select a coffee product to see available options"})}),c==="kcups"&&de&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Select Flavor"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:(F=(W=de.variants)==null?void 0:W.nodes)==null?void 0:F.map(s=>{var p,v;return e.jsx("button",{onClick:()=>U(s.id),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-300 ease-out text-left transform hover:scale-105 hover:-translate-y-0.5 ${H===s.id?"border-army-600 bg-army-600 text-white shadow-md hover:bg-army-700 hover:shadow-lg":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600 hover:bg-army-50 hover:shadow-md"}`,children:((v=(p=s.selectedOptions)==null?void 0:p.find(P=>P.name.toLowerCase().includes("title")||P.name.toLowerCase().includes("flavor")))==null?void 0:v.value)||s.title||"K-Cup Variant"},s.id)})})]}),c&&(c==="coffee"&&y&&(m&&g||N&&i&&j||((M=h.data)==null?void 0:M.product)&&(h.data.product.id==="gid://shopify/Product/10111587647803"||h.data.product.title.toLowerCase().includes("roasters box"))&&g)||c==="kcups"&&H)&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Delivery Frequency"}),e.jsxs("div",{className:"relative",ref:we,children:[e.jsx("button",{type:"button",onClick:()=>je(!ye),className:"w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 hover:border-gray-400 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium text-gray-900",children:dt.label}),e.jsx("svg",{className:`w-5 h-5 text-gray-400 transition-transform duration-200 ${ye?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),ye&&e.jsx("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:ue.map(s=>e.jsx("button",{type:"button",onClick:()=>{at(s.value),je(!1)},className:`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg ${ce===s.value?"bg-army-50 border-l-4 border-army-500":""}`,children:e.jsx("span",{className:`font-medium ${ce===s.value?"text-army-900":"text-gray-900"}`,children:s.label})},s.value))})]})]})]}),c&&(c==="coffee"&&y&&(m&&g||N&&i&&j||((Q=h.data)==null?void 0:Q.product)&&(h.data.product.id==="gid://shopify/Product/10111587647803"||h.data.product.title.toLowerCase().includes("roasters box"))&&g)||c==="kcups"&&H)&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>Ie(Math.max(1,ie-1)),className:"w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"text-xl font-semibold text-gray-900 min-w-[3rem] text-center",children:ie}),e.jsx("button",{onClick:()=>Ie(ie+1),className:"w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]})]}),e.jsx("div",{className:"space-y-3",children:(()=>{var v,P,V,C,_,d,f,n,me,he,Ae,De,Ge;const s=ue.find(I=>I.value===ce);let p=null;if(c==="coffee"&&y&&((v=h.data)!=null&&v.product)){const I=h.data.product.id==="gid://shopify/Product/8965076156731"||qe(h.data.product).some(z=>z.name.includes("Flavor #")),Ne=h.data.product.id==="gid://shopify/Product/10111589941563",ke=h.data.product.id==="gid://shopify/Product/10111587647803";I&&N&&i&&j?p=(V=(P=h.data.product.variants)==null?void 0:P.nodes)==null?void 0:V.find(z=>{var Z,D,Ye;const q=z.selectedOptions||[],xe=((Z=q.find(X=>X.name==="Flavor #1"))==null?void 0:Z.value)===N,R=((D=q.find(X=>X.name==="Flavor #2"))==null?void 0:D.value)===i,fe=((Ye=q.find(X=>X.name==="Flavor #3"))==null?void 0:Ye.value)===j;return xe&&R&&fe}):Ne&&m&&g?p=(_=(C=h.data.product.variants)==null?void 0:C.nodes)==null?void 0:_.find(z=>{var fe,Z;const q=z.selectedOptions||[],xe=((fe=q.find(D=>D.name==="Box Selection"))==null?void 0:fe.value)===m,R=((Z=q.find(D=>D.name==="Type"))==null?void 0:Z.value)===g;return xe&&R}):ke&&g?p=(f=(d=h.data.product.variants)==null?void 0:d.nodes)==null?void 0:f.find(z=>(z.selectedOptions||[]).find(R=>(R.name==="Type"||R.name==="Grind"||R.name==="Grind Type"||R.name.toLowerCase().includes("type")||R.name.toLowerCase().includes("grind"))&&R.value===g)):m&&g&&(p=ct(h.data.product,{Size:m,type:g}))}else c==="kcups"&&H&&de&&(p=(me=(n=de.variants)==null?void 0:n.nodes)==null?void 0:me.find(I=>I.id===H));if(p&&s)return e.jsx(Qe,{onClick:()=>nt("cart"),lines:[{merchandiseId:p.id,quantity:ie,sellingPlanId:s.sellingPlanId}],className:`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-colors duration-200 text-white ${c==="coffee"?"bg-army-600 hover:bg-army-700":"bg-amber-600 hover:bg-amber-700"}`,children:"Start Subscription"});if(c==="coffee"&&y&&s&&!p){const I=m&&g,Ne=N&&i&&j,ke=m&&g,z=((he=h.data)==null?void 0:he.product)&&(h.data.product.id==="gid://shopify/Product/10111587647803"||h.data.product.title.toLowerCase().includes("roasters box"))&&g;if(I||Ne||ke||z)return e.jsxs("div",{className:"text-center py-4 text-red-500 text-sm",children:["Unable to find matching variant. Please try different selections.",e.jsx("br",{}),e.jsxs("span",{className:"text-xs text-gray-500",children:["Product: ",((De=(Ae=h.data)==null?void 0:Ae.product)==null?void 0:De.title)||"Unknown"," | Selections: Size=",m,", Type=",g,", Flavors=",N,",",i,",",j]})]})}return c==="coffee"&&y?e.jsxs("div",{className:"text-center py-4 text-gray-500 text-xs",children:["Debug: Product selected but no button.",e.jsx("br",{}),"Product: ",J==null?void 0:J.title,e.jsx("br",{}),"Fetcher data: ",(Ge=h.data)!=null&&Ge.product?"Yes":"No",e.jsx("br",{}),"Selections: Size=",m,", Type=",g,e.jsx("br",{}),"Frequency: ",s==null?void 0:s.label,e.jsx("br",{}),"Variant found: ",p?"Yes":"No"]}):null})()})]}),e.jsx("div",{className:"mt-6",children:e.jsx("a",{href:"/pages/subscriptions",className:"block w-full text-center bg-army-600 hover:bg-army-700 py-3 px-6 rounded-lg font-medium transition-colors duration-200",style:{color:"white"},children:e.jsx("span",{className:"text-white",children:"Learn More About Subscriptions"})})})]})]})})]},t)}return e.jsxs("div",{ref:t==="coffee"?ae:t==="bundles"?pe:le,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"188px"},children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-army-100 rounded-xl flex items-center justify-center mr-4 text-army-600",children:r.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-4xl font-bold transition-colors duration-200 ${w===t?"text-amber-600":"text-gray-900"}`,style:{fontFamily:"var(--font-title)"},children:r.title}),e.jsx("p",{className:"text-lg text-gray-600 mt-1",children:r.description})]})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${w===t?"bg-amber-100 text-amber-800":"bg-army-100 text-army-800"}`,children:t==="subscriptions"?"1 service":`${o.length} ${o.length===1?"item":"items"}`})})]}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${w===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),((o==null?void 0:o.length)||0)>0?e.jsx("div",{className:`grid gap-6 ${ee==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:o.map((c,L)=>{const y=c.id==="gid://shopify/Product/10111589941563"||c.id==="gid://shopify/Product/10111587647803"?{...c,availableForSale:!0}:c;return e.jsx(gt,{product:y,loading:L<6?"eager":"lazy",viewMode:ee},c.id)})}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 text-gray-400",children:e.jsx("div",{className:"w-12 h-12",children:r.icon})}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["No ",r.title.toLowerCase()," found"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:((l==null?void 0:l.length)||0)===0?`We don't have any ${r.title.toLowerCase()} products yet.`:"Try adjusting your filters to see more results."}),((l==null?void 0:l.length)||0)>0&&e.jsx("button",{onClick:()=>{const c=new URLSearchParams,L=S.get("section");L&&c.set("section",L),et(`?${c.toString()}`)},className:"btn-primary",children:"Clear All Filters"})]})]},t)})})}),e.jsx("div",{className:"bg-army-600 py-20",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center text-white",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("span",{className:"inline-flex items-center text-amber-300 text-sm font-medium mb-4",children:[e.jsx("img",{src:"/coffeeicon.svg",alt:"Coffee",className:"w-5 h-5 mr-2 filter brightness-0 invert"}),"Our Origin Story"]}),e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",style:{fontFamily:"var(--font-title)"},children:"Why Nicaragua?"}),e.jsx("div",{className:"w-24 h-1 bg-amber-500 rounded mx-auto mb-8"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 text-left",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 text-amber-300",children:"Perfect Growing Conditions"}),e.jsx("p",{className:"text-lg leading-relaxed opacity-90",children:"Nicaragua's volcanic soil, high altitude regions, and tropical climate create ideal conditions for coffee cultivation. The mountainous regions provide perfect elevation and temperature variations that allow coffee cherries to develop slowly, concentrating their flavors."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 text-amber-300",children:"Exceptional Flavor Profile"}),e.jsx("p",{className:"text-lg leading-relaxed opacity-90",children:"Nicaraguan coffee is renowned for its well-balanced flavor profile, featuring bright acidity, medium to full body, and complex flavor notes ranging from chocolate and nuts to fruity and floral undertones - perfect for any brewing method."})]})]}),e.jsx("div",{className:"mt-12 p-8 bg-army-700 rounded-xl",children:e.jsx("p",{className:"text-xl leading-relaxed",children:e.jsx("strong",{children:"Every cup of Big River Coffee tells the story of Nicaragua's rich coffee heritage, sustainable farming practices, and the passionate farmers who make it all possible."})})})]})})})]})]})," "]})});export{Yt as default,Gt as meta};
