window.__reactRouterManifest={"entry":{"module":"/assets/entry.client-BzhQr2-P.js","imports":["/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-BIBkYi5k.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":true,"module":"/assets/root-B9CX9xqg.js","imports":["/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-BIBkYi5k.js","/assets/root-B6KRH2x_.js","/assets/index-CtaMWxPM.js","/assets/with-props-CbdrmLzL.js","/assets/Aside-DOuHpYsm.js","/assets/CartMain-DTnH-KNN.js","/assets/variants-0wbUisc9.js","/assets/ProductPrice-B-a9ZZ0k.js","/assets/Money-CflnwKbq.js","/assets/Image-aRbI9IFn.js","/assets/search-DOeYwaXi.js"],"css":[]},"routes/[robots.txt]":{"id":"routes/[robots.txt]","parentId":"root","path":"robots.txt","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_robots.txt_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale)":{"id":"routes/($locale)","parentId":"root","path":":locale?","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs.$blogHandle.$articleHandle":{"id":"routes/($locale).blogs.$blogHandle.$articleHandle","parentId":"routes/($locale)","path":"blogs/:blogHandle/:articleHandle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._blogHandle._articleHandle-DwIa9ks8.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/Image-aRbI9IFn.js"],"css":[]},"routes/($locale).api.$version.[graphql.json]":{"id":"routes/($locale).api.$version.[graphql.json]","parentId":"routes/($locale)","path":"api/:version/graphql.json","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api._version._graphql.json_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).sitemap.$type.$page[.xml]":{"id":"routes/($locale).sitemap.$type.$page[.xml]","parentId":"routes/($locale)","path":"sitemap/:type/:page.xml","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).sitemap._type._page_.xml_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs.$blogHandle._index":{"id":"routes/($locale).blogs.$blogHandle._index","parentId":"routes/($locale)","path":"blogs/:blogHandle","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._blogHandle._index-DDlfkC4_.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/PaginatedResourceSection-DBdwsehO.js","/assets/Image-aRbI9IFn.js","/assets/index-CtaMWxPM.js"],"css":[]},"routes/($locale).collections.all-coffees":{"id":"routes/($locale).collections.all-coffees","parentId":"routes/($locale)","path":"collections/all-coffees","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections.all-coffees-BjRwr278.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).products.first-one-free":{"id":"routes/($locale).products.first-one-free","parentId":"routes/($locale)","path":"products/first-one-free","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products.first-one-free-DG040os5.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-CtaMWxPM.js","/assets/AddToCartButton-PbdfRk7P.js","/assets/Aside-DOuHpYsm.js","/assets/CrossSellProducts-DgRnyoVj.js","/assets/useSelectedOptionInUrlParam-C_GP6A91.js","/assets/Image-aRbI9IFn.js","/assets/Money-CflnwKbq.js","/assets/getProductOptions-DRO1RMF1.js"],"css":[]},"routes/($locale).products.roasters-box":{"id":"routes/($locale).products.roasters-box","parentId":"routes/($locale)","path":"products/roasters-box","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products.roasters-box-CiK5YKxJ.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-CtaMWxPM.js","/assets/Aside-DOuHpYsm.js","/assets/AddToCartButton-PbdfRk7P.js","/assets/getProductOptions-DRO1RMF1.js","/assets/Image-aRbI9IFn.js","/assets/Money-CflnwKbq.js","/assets/useSelectedOptionInUrlParam-C_GP6A91.js"],"css":[]},"routes/($locale).collections.$handle":{"id":"routes/($locale).collections.$handle","parentId":"routes/($locale)","path":"collections/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections._handle-Ba5E-2az.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-CtaMWxPM.js","/assets/ProductCard-Bh04Afjv.js","/assets/variants-0wbUisc9.js","/assets/AddToCartButton-PbdfRk7P.js","/assets/Image-aRbI9IFn.js","/assets/Money-CflnwKbq.js"],"css":[]},"routes/($locale).pages.subscriptions":{"id":"routes/($locale).pages.subscriptions","parentId":"routes/($locale)","path":"pages/subscriptions","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.subscriptions-BHFPs7k7.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).account_.authorize":{"id":"routes/($locale).account_.authorize","parentId":"routes/($locale)","path":"account/authorize","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.authorize-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).collections._index":{"id":"routes/($locale).collections._index","parentId":"routes/($locale)","path":"collections","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections._index-CgVzo4ik.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/PaginatedResourceSection-DBdwsehO.js","/assets/Image-aRbI9IFn.js","/assets/index-CtaMWxPM.js"],"css":[]},"routes/($locale).account_.register":{"id":"routes/($locale).account_.register","parentId":"routes/($locale)","path":"account/register","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.register-BWLU6d4X.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).policies.$handle":{"id":"routes/($locale).policies.$handle","parentId":"routes/($locale)","path":"policies/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).policies._handle-CLJsgTLs.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).products.$handle":{"id":"routes/($locale).products.$handle","parentId":"routes/($locale)","path":"products/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products._handle-FgX7mGK1.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-CtaMWxPM.js","/assets/ProductPrice-B-a9ZZ0k.js","/assets/Image-aRbI9IFn.js","/assets/AddToCartButton-PbdfRk7P.js","/assets/Aside-DOuHpYsm.js","/assets/CrossSellProducts-DgRnyoVj.js","/assets/getProductOptions-DRO1RMF1.js","/assets/Money-CflnwKbq.js","/assets/useSelectedOptionInUrlParam-C_GP6A91.js"],"css":[]},"routes/($locale).account_.logout":{"id":"routes/($locale).account_.logout","parentId":"routes/($locale)","path":"account/logout","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.logout-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).collections.all":{"id":"routes/($locale).collections.all","parentId":"routes/($locale)","path":"collections/all","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections.all-BviVM-Mq.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/ProductCard-Bh04Afjv.js","/assets/AddToCartButton-PbdfRk7P.js","/assets/Aside-DOuHpYsm.js","/assets/OptimizedVideo-Dqf1l_zF.js","/assets/scrollAnimations-D2F8dg76.js","/assets/Money-CflnwKbq.js","/assets/getProductOptions-DRO1RMF1.js","/assets/variants-0wbUisc9.js","/assets/Image-aRbI9IFn.js","/assets/index-CtaMWxPM.js"],"css":[]},"routes/($locale).pages.nicaragua":{"id":"routes/($locale).pages.nicaragua","parentId":"routes/($locale)","path":"pages/nicaragua","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.nicaragua-CibDijgu.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).policies._index":{"id":"routes/($locale).policies._index","parentId":"routes/($locale)","path":"policies","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).policies._index-B7O59hrI.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).account_.login":{"id":"routes/($locale).account_.login","parentId":"routes/($locale)","path":"account/login","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.login-B8udr45x.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).api.newsletter":{"id":"routes/($locale).api.newsletter","parentId":"routes/($locale)","path":"api/newsletter","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api.newsletter-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).discount.$code":{"id":"routes/($locale).discount.$code","parentId":"routes/($locale)","path":"discount/:code","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).discount._code-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).pages.$handle":{"id":"routes/($locale).pages.$handle","parentId":"routes/($locale)","path":"pages/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages._handle-C_wzrY7e.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).[sitemap.xml]":{"id":"routes/($locale).[sitemap.xml]","parentId":"routes/($locale)","path":"sitemap.xml","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._sitemap.xml_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).api.products":{"id":"routes/($locale).api.products","parentId":"routes/($locale)","path":"api/products","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api.products-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs._index":{"id":"routes/($locale).blogs._index","parentId":"routes/($locale)","path":"blogs","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._index-BqEqnrA6.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/PaginatedResourceSection-DBdwsehO.js","/assets/index-CtaMWxPM.js"],"css":[]},"routes/($locale).pages.brew":{"id":"routes/($locale).pages.brew","parentId":"routes/($locale)","path":"pages/brew","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.brew-CEfa48U_.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).affiliate":{"id":"routes/($locale).affiliate","parentId":"routes/($locale)","path":"affiliate","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).affiliate-Ck2F2we5.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).our-story":{"id":"routes/($locale).our-story","parentId":"routes/($locale)","path":"our-story","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).our-story-B_mVaazE.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/OptimizedVideo-Dqf1l_zF.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).account":{"id":"routes/($locale).account","parentId":"routes/($locale)","path":"account","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account-CqlvVIxw.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).account.orders._index":{"id":"routes/($locale).account.orders._index","parentId":"routes/($locale).account","path":"orders","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.orders._index-DOSqNj75.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/PaginatedResourceSection-DBdwsehO.js","/assets/index-CtaMWxPM.js","/assets/Money-CflnwKbq.js"],"css":[]},"routes/($locale).account.orders.$id":{"id":"routes/($locale).account.orders.$id","parentId":"routes/($locale).account","path":"orders/:id","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.orders._id-l8rLsg1a.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/Money-CflnwKbq.js","/assets/Image-aRbI9IFn.js"],"css":[]},"routes/($locale).account.addresses":{"id":"routes/($locale).account.addresses","parentId":"routes/($locale).account","path":"addresses","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.addresses-CirhrgZS.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).account.profile":{"id":"routes/($locale).account.profile","parentId":"routes/($locale).account","path":"profile","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.profile-Bz-G4hzV.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).account._index":{"id":"routes/($locale).account._index","parentId":"routes/($locale).account","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account._index-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).account.$":{"id":"routes/($locale).account.$","parentId":"routes/($locale).account","path":"*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account._-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).contact":{"id":"routes/($locale).contact","parentId":"routes/($locale)","path":"contact","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).contact-CsejMv_x.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).rewards":{"id":"routes/($locale).rewards","parentId":"routes/($locale)","path":"rewards","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).rewards-nwRsOkxg.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).search":{"id":"routes/($locale).search","parentId":"routes/($locale)","path":"search","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).search-S4zPrdEi.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-CtaMWxPM.js","/assets/search-DOeYwaXi.js","/assets/Image-aRbI9IFn.js","/assets/Money-CflnwKbq.js"],"css":[]},"routes/($locale)._index":{"id":"routes/($locale)._index","parentId":"routes/($locale)","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._index-CaAE87oa.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/index-BIBkYi5k.js","/assets/OptimizedVideo-Dqf1l_zF.js"],"css":[]},"routes/($locale).cart":{"id":"routes/($locale).cart","parentId":"routes/($locale)","path":"cart","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).cart-BonWuyrO.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/jsx-runtime-C_22QpLb.js","/assets/chunk-D4RADZKF-BzETJbTh.js","/assets/CartMain-DTnH-KNN.js","/assets/index-CtaMWxPM.js","/assets/Aside-DOuHpYsm.js","/assets/variants-0wbUisc9.js","/assets/ProductPrice-B-a9ZZ0k.js","/assets/Money-CflnwKbq.js","/assets/Image-aRbI9IFn.js"],"css":[]},"routes/($locale).cart.$lines":{"id":"routes/($locale).cart.$lines","parentId":"routes/($locale).cart","path":":lines","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).cart._lines-DHLEmzUz.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]},"routes/($locale).$":{"id":"routes/($locale).$","parentId":"routes/($locale)","path":"*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._-Cp4WSwII.js","imports":["/assets/with-props-CbdrmLzL.js","/assets/chunk-D4RADZKF-BzETJbTh.js"],"css":[]}},"url":"/assets/manifest-6dab901f.js","version":"6dab901f"};