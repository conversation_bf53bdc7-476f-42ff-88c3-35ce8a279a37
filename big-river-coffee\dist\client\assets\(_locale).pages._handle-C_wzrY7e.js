import{w as o}from"./with-props-CbdrmLzL.js";import{j as r}from"./jsx-runtime-C_22QpLb.js";import{u as s}from"./chunk-D4RADZKF-BzETJbTh.js";const l=({data:e})=>[{title:`Hydrogen | ${(e==null?void 0:e.page.title)??""}`}],m=o(function(){const{page:t}=s();return r.jsxs("div",{className:"page",children:[r.jsx("header",{children:r.jsx("h1",{children:t.title})}),r.jsx("main",{dangerouslySetInnerHTML:{__html:t.body}})]})});export{m as default,l as meta};
